# == Schema Information
#
# Table name: organization_permission_controllers
#
#  id                                 :bigint           not null, primary key
#  created_at                         :datetime         not null
#  updated_at                         :datetime         not null
#  organization_id(企业ID)            :integer
#  permission_controller_id(权限控制器ID) :integer
#
# Indexes
#
#  index_org_perm_ctrl_on_org_id                    (organization_id)
#  index_org_perm_ctrl_on_org_id_and_perm_ctrl_id  (organization_id,permission_controller_id) UNIQUE
#  index_org_perm_ctrl_on_perm_ctrl_id              (permission_controller_id)
#
require "test_helper"

class OrganizationPermissionControllerTest < ActiveSupport::TestCase
  def setup
    @parent_org = Organization.create!(name: "父企业", org_type: :agent)
    @child_org = Organization.create!(name: "子企业", org_type: :solution_provider, parent: @parent_org)
    @permission_controller = PermissionController.create!(
      name: "测试控制器", 
      word: "test_controller", 
      organization: @parent_org
    )
  end

  test "should create organization permission controller" do
    opc = OrganizationPermissionController.new(
      organization: @child_org,
      permission_controller: @permission_controller
    )
    assert opc.save
  end

  test "should not allow duplicate assignments" do
    OrganizationPermissionController.create!(
      organization: @child_org,
      permission_controller: @permission_controller
    )
    
    duplicate = OrganizationPermissionController.new(
      organization: @child_org,
      permission_controller: @permission_controller
    )
    assert_not duplicate.save
  end

  test "organization should have available permission controllers from parent" do
    assert_includes @child_org.available_permission_controllers, @permission_controller
  end

  test "organization should be able to assign permission controller" do
    assert @child_org.assign_permission_controller(@permission_controller.id)
    assert @child_org.has_permission_controller?(@permission_controller.id)
  end

  test "organization should be able to remove permission controller" do
    @child_org.assign_permission_controller(@permission_controller.id)
    assert @child_org.has_permission_controller?(@permission_controller.id)
    
    @child_org.remove_permission_controller(@permission_controller.id)
    assert_not @child_org.has_permission_controller?(@permission_controller.id)
  end
end
