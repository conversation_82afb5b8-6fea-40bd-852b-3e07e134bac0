<div class="layui-card">
  <div class="layui-card-header">
    <h3><%= @organization.name %> - 权限控制器管理</h3>
  </div>
  <div class="layui-card-body">
    <div class="layui-row layui-col-space16">
      <!-- 可分配的权限控制器 -->
      <div class="layui-col-md6">
        <div class="layui-card">
          <div class="layui-card-header">可分配的权限控制器</div>
          <div class="layui-card-body">
            <% if @available_permission_controllers.any? %>
              <table class="layui-table">
                <thead>
                  <tr>
                    <th>控制器名称</th>
                    <th>控制器标识</th>
                    <th>操作</th>
                  </tr>
                </thead>
                <tbody>
                  <% @available_permission_controllers.each do |controller| %>
                    <tr>
                      <td><%= controller.name %></td>
                      <td><%= controller.word %></td>
                      <td>
                        <% unless @organization.has_permission_controller?(controller.id) %>
                          <button class="layui-btn layui-btn-xs layui-btn-primary" 
                                  onclick="assignPermissionController(<%= @organization.id %>, <%= controller.id %>)">
                            分配
                          </button>
                        <% else %>
                          <span class="layui-badge layui-bg-gray">已分配</span>
                        <% end %>
                      </td>
                    </tr>
                  <% end %>
                </tbody>
              </table>
            <% else %>
              <div class="layui-empty">
                <div class="layui-empty-icon">
                  <i class="layui-icon layui-icon-face-cry"></i>
                </div>
                <p>暂无可分配的权限控制器</p>
                <p class="layui-text">请联系父级企业管理员创建权限控制器</p>
              </div>
            <% end %>
          </div>
        </div>
      </div>

      <!-- 已分配的权限控制器 -->
      <div class="layui-col-md6">
        <div class="layui-card">
          <div class="layui-card-header">已分配的权限控制器</div>
          <div class="layui-card-body">
            <% if @assigned_permission_controllers.any? %>
              <table class="layui-table">
                <thead>
                  <tr>
                    <th>控制器名称</th>
                    <th>控制器标识</th>
                    <th>操作</th>
                  </tr>
                </thead>
                <tbody>
                  <% @assigned_permission_controllers.each do |controller| %>
                    <tr>
                      <td><%= controller.name %></td>
                      <td><%= controller.word %></td>
                      <td>
                        <button class="layui-btn layui-btn-xs layui-btn-danger" 
                                onclick="removePermissionController(<%= @organization.id %>, <%= controller.id %>)">
                          移除
                        </button>
                      </td>
                    </tr>
                  <% end %>
                </tbody>
              </table>
            <% else %>
              <div class="layui-empty">
                <div class="layui-empty-icon">
                  <i class="layui-icon layui-icon-face-surprised"></i>
                </div>
                <p>暂无已分配的权限控制器</p>
              </div>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  function assignPermissionController(organizationId, permissionControllerId) {
    layer.confirm('确认分配此权限控制器吗？', function(index){
      layer.close(index);
      $.ajax({
        type: 'POST',
        url: `/admin/organizations/${organizationId}/assign_permission_controller`,
        data: {
          permission_controller_id: permissionControllerId
        },
        success: function(response) {
          if (response.status) {
            layer.msg(response.msg || '分配成功', {icon: 1});
            location.reload();
          } else {
            layer.msg(response.msg || '分配失败', {icon: 2});
          }
        },
        error: function() {
          layer.msg('操作失败', {icon: 2});
        }
      });
    });
  }

  function removePermissionController(organizationId, permissionControllerId) {
    layer.confirm('确认移除此权限控制器吗？', function(index){
      layer.close(index);
      $.ajax({
        type: 'DELETE',
        url: `/admin/organizations/${organizationId}/remove_permission_controller`,
        data: {
          permission_controller_id: permissionControllerId
        },
        success: function(response) {
          if (response.status) {
            layer.msg(response.msg || '移除成功', {icon: 1});
            location.reload();
          } else {
            layer.msg(response.msg || '移除失败', {icon: 2});
          }
        },
        error: function() {
          layer.msg('操作失败', {icon: 2});
        }
      });
    });
  }
</script>
