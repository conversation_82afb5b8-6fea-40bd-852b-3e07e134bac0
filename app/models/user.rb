# == Schema Information
#
# Table name: users
#
#  id                      :bigint           not null, primary key
#  actived_at(激活时间)    :datetime
#  avatar                  :string(200)
#  deleted_at              :datetime
#  email(邮箱)             :string
#  is_admin(是否管理员)    :boolean          default(FALSE)
#  last_login_at           :datetime
#  locked_at(锁定时间)     :datetime
#  name                    :string(200)
#  password_digest         :string
#  phone                   :string(200)
#  status                  :integer
#  created_at              :datetime         not null
#  updated_at              :datetime         not null
#  feishu_user_id          :string
#  organization_id(组织ID) :integer
#  recommend_user_id       :integer
#
# Indexes
#
#  index_users_on_feishu_user_id   (feishu_user_id) UNIQUE
#  index_users_on_organization_id  (organization_id)
#
class User < ApplicationRecord
  acts_as_paranoid
  has_secure_password

  # mount_uploader :avatar, UserAvatarUploader

  validates_format_of :phone, :with => /^[12][\d]{10}$/, :multiline => true, :message => '手机号无效', if: Proc.new{|x| x.phone.present?}
  validates :name, :password_digest, :phone, presence: true
  validates_uniqueness_of :phone

  belongs_to :recommend_user, foreign_key: "recommend_user_id", class_name: "User", optional: true
  belongs_to :organization
  has_many :user_roles, dependent: :destroy
  has_many :roles, through: :user_roles
  has_many :plans, dependent: :destroy
  has_many :duty_plans, foreign_key: "duty_user_id", class_name: "Plan", dependent: :destroy

  # has_many :user_login_infos, dependent: :destroy
  # has_many :authentications, dependent: :destroy
  has_many :project_users, dependent: :destroy
  has_many :project_role_configs, through: :project_users
  has_many :approval_step_users, dependent: :destroy
  has_many :user_notices, dependent: :destroy

  enum status: {active: 1, freeze: 2}

  def current_account
    user_account = UserAccount.find_by(user_id: self.id)
    if user_account.blank?
      user_account = UserAccount.create({balance: 0, user_id: self.id})
    end
    user_account
  end

  def avatar_url
    self.avatar || '/assets/QQ.png'
  end

  def self.file_url(url)
    open_url = open(url) rescue ''
    if Rails.env.test? || open_url.blank?
      file = nil
    else
      file = Tempfile.new(['temp', ".#{open_url.content_type.split("/")[1]}"])
      file.binmode
      file.write open_url.read
      file
    end
    editor_file = EditorFile.create(file: file, file_type: 'image', file_name: file.original_filename)
    editor_file.file_url
  end

  def self.ransackable_attributes(auth_object = nil)
    %w[name phone]
  end

  def self.ransackable_associations(auth_object = nil)
    ["recharge_orders", "recommend_user", "user_chat_sends", "user_chats", "user_login_infos"]
  end

  def self.select_many_user(organization_id)
    User.where(organization_id: organization_id).map do |user|
      {
        name: user.name,
        value: user.id
      }
    end
  end
end
