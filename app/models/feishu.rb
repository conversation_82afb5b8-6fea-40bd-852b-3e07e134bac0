class Feishu
  def initialize
    @app_id = Settings.feishu.app_id
    @app_secret = Settings.feishu.app_secret
  end

  def get_token
    Rails.cache.fetch("feishu_token", expires_in: 1.hour) do
      uri = URI.parse("https://open.feishu.cn/open-apis/auth/v3/app_access_token/internal")
      data = {
        app_id: @app_id,
        app_secret: @app_secret
      }
      http = Net::HTTP.new(uri.host, uri.port)
      http.use_ssl = true
      request = Net::HTTP::Post.new(uri.request_uri, 'Content-Type' => 'application/json; charset=utf-8')
      request.body = data.to_json
      response = http.request(request)
      result = JSON.parse(response.body)
      if result['code'] == 0
        result['tenant_access_token']
      end
    end
  end

  def send_message(user_list, message)
    uri = URI.parse("https://open.feishu.cn/open-apis/message/v4/batch_send")
    data = {
      user_ids: user_list,
      msg_type: "text",
      content: {
        "text": message
      }
    }
    http = Net::HTTP.new(uri.host, uri.port)
    http.use_ssl = true
    request = Net::HTTP::Post.new(uri.request_uri)
    request['Authorization'] = "Bearer #{get_token}"
    request['Content-Type'] = "application/json; charset=utf-8"
    request.body = data.to_json
    response = http.request(request)
    result = JSON.parse(response.body)
    if result['code'] == 0
      result
    else
      Rails.logger.error("Feishu send_message error: #{result['msg']}")
      []
    end
  end

  def get_users_id(phone_list)
    uri = URI.parse("https://open.feishu.cn/open-apis/contact/v3/users/batch_get_id?user_id_type=user_id")
    data = {
      mobiles: phone_list
    }
    http = Net::HTTP.new(uri.host, uri.port)
    http.use_ssl = true
    request = Net::HTTP::Post.new(uri.request_uri)
    request['Authorization'] = "Bearer #{get_token}"
    request['Content-Type'] = "application/json; charset=utf-8"
    request.body = data.to_json
    response = http.request(request)
    result = JSON.parse(response.body)
    if result['code'] == 0
      result['data']['user_list'].map { |user| user['user_id'] }
    else
      Rails.logger.error("Feishu get_users_id error: #{result['msg']}")
      []
    end
  end
end
